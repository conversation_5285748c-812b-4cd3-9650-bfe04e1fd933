#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Milestone Instruction Generator
 *
 * Generates comprehensive, agent-specific instructions for milestone execution
 * Based on milestone specifications and agent-specific rules
 */
class InstructionGenerator {
  constructor() {
    this.supportedAgents = ["cursor", "copilot", "augment", "claude"];
  }

  /**
   * Main entry point - generates instructions for a milestone and agent
   */
  async generateInstructions(milestoneFile, agentType, outputFile) {
    try {
      console.log(`🔍 Parsing milestone: ${milestoneFile}`);
      const milestone = await this.parseMilestone(milestoneFile);

      console.log(`📖 Loading agent rules: ${agentType}`);
      const agentRules = await this.loadAgentRules(agentType);

      console.log(`🔨 Generating execution steps...`);
      const steps = this.generateExecutionSteps(milestone);

      console.log(`✅ Adding validation criteria...`);
      const validatedSteps = this.addValidationCriteria(steps, milestone);

      console.log(`📝 Formatting instructions...`);
      const instructions = this.formatInstructions(
        milestone,
        validatedSteps,
        agentRules,
        agentType
      );

      console.log(`💾 Writing to: ${outputFile}`);
      await this.writeInstructions(instructions, outputFile);

      return true;
    } catch (error) {
      console.error(`❌ Error generating instructions: ${error.message}`);
      return false;
    }
  }

  /**
   * Parse milestone MDX file and extract key information
   */
  async parseMilestone(milestoneFile) {
    const content = fs.readFileSync(milestoneFile, "utf8");

    // Extract frontmatter
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    const frontmatter = frontmatterMatch
      ? this.parseFrontmatter(frontmatterMatch[1])
      : {};

    // Extract main content
    const mainContent = content.replace(/^---\n[\s\S]*?\n---\n/, "");

    // Extract sections
    const sections = this.extractSections(mainContent);

    return {
      frontmatter,
      content: mainContent,
      sections,
      title: frontmatter.title || "Unknown Milestone",
      id: frontmatter.milestone || path.basename(milestoneFile, ".mdx"),
      description:
        frontmatter.description ||
        sections.overview ||
        "No description available"
    };
  }

  /**
   * Parse YAML frontmatter
   */
  parseFrontmatter(frontmatterText) {
    const frontmatter = {};
    const lines = frontmatterText.split("\n");

    for (const line of lines) {
      const match = line.match(/^(\w+):\s*(.+)$/);
      if (match) {
        frontmatter[match[1]] = match[2].replace(/^["']|["']$/g, "");
      }
    }

    return frontmatter;
  }

  /**
   * Extract sections from markdown content
   */
  extractSections(content) {
    const sections = {};
    const lines = content.split("\n");
    let currentSection = null;
    let currentContent = [];

    for (const line of lines) {
      const headerMatch = line.match(/^#{1,3}\s+(.+)$/);

      if (headerMatch) {
        // Save previous section
        if (currentSection) {
          sections[currentSection] = currentContent.join("\n").trim();
        }

        // Start new section
        currentSection = headerMatch[1]
          .toLowerCase()
          .replace(/[^\w\s]/g, "")
          .replace(/\s+/g, "_");
        currentContent = [];
      } else if (currentSection) {
        currentContent.push(line);
      }
    }

    // Save last section
    if (currentSection) {
      sections[currentSection] = currentContent.join("\n").trim();
    }

    return sections;
  }

  /**
   * Load agent-specific rules from existing documentation
   */
  async loadAgentRules(agentType) {
    const agentRulesPath = path.join(
      __dirname,
      "..",
      "tech-specs",
      "process",
      "agent-rules",
      `${agentType}.mdx`
    );
    const coreRulesPath = path.join(
      __dirname,
      "..",
      "tech-specs",
      "process",
      "agent-rules",
      "core.mdx"
    );

    let agentRules = "";
    let coreRules = "";

    try {
      if (fs.existsSync(agentRulesPath)) {
        agentRules = fs.readFileSync(agentRulesPath, "utf8");
      }

      if (fs.existsSync(coreRulesPath)) {
        coreRules = fs.readFileSync(coreRulesPath, "utf8");
      }
    } catch (error) {
      console.warn(`⚠️  Could not load agent rules: ${error.message}`);
    }

    return {
      agentSpecific: agentRules,
      core: coreRules,
      summary: this.extractRulesSummary(agentRules, coreRules)
    };
  }

  /**
   * Extract key rules summary from agent documentation
   */
  extractRulesSummary(agentRules, coreRules) {
    const rules = [];

    // Extract rules from both agent-specific and core rules
    const allRules = agentRules + "\n" + coreRules;

    // Look for numbered lists, bullet points, or "must" statements
    const rulePatterns = [
      /^\d+\.\s+(.+)$/gm, // Numbered lists
      /^[-*]\s+(.+)$/gm, // Bullet points
      /must\s+(.+?)(?:\.|$)/gi, // "Must" statements
      /should\s+(.+?)(?:\.|$)/gi // "Should" statements
    ];

    for (const pattern of rulePatterns) {
      const matches = allRules.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && match[1].length > 10) {
          rules.push(match[1].trim());
        }
      }
    }

    return rules.slice(0, 10); // Top 10 most important rules
  }

  /**
   * Generate logical execution steps from milestone content
   */
  generateExecutionSteps(milestone) {
    console.log('🔍 Analyzing milestone structure...');

    // First, try to extract from implementation approach
    const implementationSteps = this.extractFromImplementationSection(milestone);
    if (implementationSteps.length > 0) {
      console.log(`✅ Found ${implementationSteps.length} implementation steps`);
      return implementationSteps;
    }

    // Second, try to extract from deliverables
    const deliverableSteps = this.extractFromDeliverables(milestone);
    if (deliverableSteps.length > 0) {
      console.log(`✅ Found ${deliverableSteps.length} deliverable steps`);
      return deliverableSteps;
    }

    // Third, extract from main content structure
    const contentSteps = this.extractFromMainContent(milestone);
    if (contentSteps.length > 0) {
      console.log(`✅ Found ${contentSteps.length} content-based steps`);
      return contentSteps;
    }

    // Fallback: create basic steps from milestone goal
    console.log('⚠️ Using fallback step generation');
    return this.createFallbackSteps(milestone);
  }

  /**
   * Extract steps from implementation approach section
   */
  extractFromImplementationSection(milestone) {
    const sections = milestone.sections;
    const implSections = ['implementation_approach', 'implementation', 'approach', 'execution_plan'];

    for (const sectionKey of implSections) {
      if (sections[sectionKey]) {
        console.log(`📋 Found implementation section: ${sectionKey}`);
        return this.parseStepsFromText(sections[sectionKey], 'implementation');
      }
    }

    return [];
  }

  /**
   * Extract steps from deliverables section
   */
  extractFromDeliverables(milestone) {
    const sections = milestone.sections;
    const deliverableSections = ['deliverables', 'tasks', 'requirements', 'objectives'];

    for (const sectionKey of deliverableSections) {
      if (sections[sectionKey]) {
        console.log(`📦 Found deliverables section: ${sectionKey}`);
        return this.parseStepsFromText(sections[sectionKey], 'deliverable');
      }
    }

    return [];
  }

  /**
   * Extract steps from main content structure
   */
  extractFromMainContent(milestone) {
    const content = milestone.content;

    // Look for step-like patterns in the main content
    const stepPatterns = [
      /^#{2,3}\s+(?:Step|Task|Phase)\s*\d*[:\-]?\s*(.+)$/gmi,
      /^#{2,3}\s+(\d+\.\s*.+)$/gm,
      /^\d+\.\s+(.+)$/gm
    ];

    const steps = [];
    const lines = content.split('\n');

    for (const pattern of stepPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && match[1].length > 10 && !match[1].includes('SC-')) {
          const stepTitle = match[1].trim();
          const description = this.extractStepDescription(content, stepTitle);

          steps.push({
            title: stepTitle,
            description: description,
            order: steps.length + 1,
            type: 'content'
          });
        }
      }
    }

    return steps;
  }

  /**
   * Extract detailed description for a step from surrounding content
   */
  extractStepDescription(content, stepTitle) {
    const lines = content.split("\n");
    let description = "";
    let capturing = false;

    for (const line of lines) {
      if (line.includes(stepTitle)) {
        capturing = true;
        continue;
      }

      if (capturing) {
        if (line.match(/^#{1,3}\s+/) || line.match(/^\d+\.\s+/)) {
          // Next section/step found
          break;
        }

        if (line.trim()) {
          description += line + "\n";
        }
      }
    }

    return description.trim() || "No detailed description available";
  }

  /**
   * Add validation criteria to each step
   */
  addValidationCriteria(steps, milestone) {
    return steps.map((step) => ({
      ...step,
      validation: this.generateValidationCriteria(step, milestone)
    }));
  }

  /**
   * Generate validation criteria for a step
   */
  generateValidationCriteria(step, milestone) {
    const criteria = [];

    // Common validation patterns based on step content
    const stepText = (step.title + " " + step.description).toLowerCase();

    if (stepText.includes("create") || stepText.includes("scaffold")) {
      criteria.push("Directory/file structure created correctly");
      criteria.push("All required files present");
    }

    if (
      stepText.includes("package") ||
      stepText.includes("npm") ||
      stepText.includes("pnpm")
    ) {
      criteria.push("Package.json contains required dependencies");
      criteria.push("Package installs without errors");
      criteria.push("Build command succeeds");
    }

    if (stepText.includes("typescript") || stepText.includes("ts")) {
      criteria.push("TypeScript compilation succeeds");
      criteria.push("No TypeScript errors");
    }

    if (stepText.includes("test")) {
      criteria.push("All tests pass");
      criteria.push("Test coverage meets requirements");
    }

    if (stepText.includes("config") || stepText.includes("setup")) {
      criteria.push("Configuration files are valid");
      criteria.push("Settings applied correctly");
    }

    // Default validation if no specific criteria found
    if (criteria.length === 0) {
      criteria.push("Step completed as described");
      criteria.push("No errors in console/logs");
    }

    return criteria;
  }

  /**
   * Format comprehensive instructions for the agent
   */
  formatInstructions(milestone, steps, agentRules, agentType) {
    const timestamp = new Date().toISOString().split("T")[0];

    let instructions = `---
title: "Execution Instructions: ${milestone.title}"
milestone: "${milestone.id}"
agent: "${agentType}"
generated: "${timestamp}"
status: "Ready for Execution"
---

# ${milestone.title} - Execution Instructions for ${
      agentType.charAt(0).toUpperCase() + agentType.slice(1)
    }

## 🎯 Milestone Overview

**Goal**: ${milestone.description}
**Milestone ID**: ${milestone.id}
**Target Agent**: ${agentType}
**Generated**: ${timestamp}

## 📋 Pre-Execution Checklist

Before starting, ensure you have:

- [ ] Read and understood the agent-specific rules for ${agentType}
- [ ] Verified your development environment is properly set up
- [ ] Confirmed you have access to all required tools and dependencies
- [ ] Reviewed the milestone specification thoroughly

### Key Rules for ${
      agentType.charAt(0).toUpperCase() + agentType.slice(1)
    } Agents:

${this.formatAgentRules(agentRules)}

## 🔨 Execution Steps

Execute the following steps in order. Each step includes validation criteria that must be met before proceeding to the next step.

`;

    // Add each step with detailed instructions
    steps.forEach((step, index) => {
      instructions += `### Step ${index + 1}: ${step.title}

**Description**: ${step.description}

**Validation Criteria**:
${step.validation.map((criteria) => `- [ ] ${criteria}`).join("\n")}

**Commands to verify**:
\`\`\`bash
# Add specific validation commands here based on step type
# Example: ls -la, pnpm build, npm test, etc.
\`\`\`

---

`;
    });

    instructions += `## 🎯 Final Success Criteria

After completing all steps, verify the milestone is successful by checking:

- [ ] All individual step validations pass
- [ ] Overall milestone objectives are met
- [ ] No errors in build/test processes
- [ ] All deliverables are present and functional

## 📝 Execution Notes

**Important**:
- Work through steps sequentially - do not skip ahead
- Validate each step before proceeding to the next
- Document any issues or deviations in your execution log
- If you encounter problems, refer back to the agent rules and troubleshooting guides

## 🚨 If Something Goes Wrong

1. **Check the validation criteria** - ensure all requirements are met
2. **Review agent-specific rules** - you may have missed a key requirement
3. **Consult troubleshooting documentation** in docs/tech-specs/process/
4. **Document the issue** for future improvement of these instructions

---

**Generated by**: Milestone Instruction Generator
**Last Updated**: ${timestamp}
`;

    return instructions;
  }

  /**
   * Format agent rules for inclusion in instructions
   */
  formatAgentRules(agentRules) {
    if (agentRules.summary && agentRules.summary.length > 0) {
      return agentRules.summary
        .slice(0, 5)
        .map((rule) => `- ${rule}`)
        .join("\n");
    }

    return `- Follow all guidelines in your agent-specific documentation
- Work systematically through each step
- Validate your work before proceeding
- Document any issues or questions`;
  }

  /**
   * Write instructions to output file
   */
  async writeInstructions(instructions, outputFile) {
    const outputDir = path.dirname(outputFile);

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputFile, instructions, "utf8");
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.length !== 3) {
    console.error(
      "Usage: node instruction-generator.mjs <milestone-file> <agent-type> <output-file>"
    );
    process.exit(1);
  }

  const [milestoneFile, agentType, outputFile] = args;

  const generator = new InstructionGenerator();
  const success = await generator.generateInstructions(
    milestoneFile,
    agentType,
    outputFile
  );

  process.exit(success ? 0 : 1);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error("Fatal error:", error);
    process.exit(1);
  });
}
