#!/usr/bin/env bash
set -euo pipefail

# Cleanup Analysis Files Script
# Removes all milestone analysis artifacts to prepare for redesign

echo "🧹 Cleaning up milestone analysis artifacts..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warn() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if we're in the right directory
if [[ ! -f "package.json" ]] || [[ ! -d "docs/scripts" ]]; then
    log_error "Please run this script from the repository root"
    exit 1
fi

echo ""
log_info "Current git status:"
git status --short

echo ""
log_warn "This will remove all milestone analysis files and reset the repository"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Cleanup cancelled"
    exit 0
fi

echo ""
log_info "Step 1: Unstaging all staged changes..."
git restore --staged .
log_success "Staged changes unstaged"

echo ""
log_info "Step 2: Discarding unstaged changes to analysis files..."
git restore docs/scripts/milestone-analysis/analyze-milestone-core.mjs 2>/dev/null || true
git restore docs/scripts/milestone-analysis/milestone-analysis-config.yml 2>/dev/null || true
log_success "Unstaged changes discarded"

echo ""
log_info "Step 3: Removing untracked analysis files..."
if [[ -f "docs/tech-specs/guides/automated-analysis-guide.mdx" ]]; then
    rm "docs/tech-specs/guides/automated-analysis-guide.mdx"
    log_success "Removed automated-analysis-guide.mdx"
fi

echo ""
log_info "Step 4: Removing milestone analysis directory..."
if [[ -d "docs/scripts/milestone-analysis" ]]; then
    rm -rf "docs/scripts/milestone-analysis"
    log_success "Removed milestone-analysis directory"
fi

echo ""
log_info "Step 5: Removing analysis-related files..."
analysis_files=(
    "docs/tech-specs/adrs/adr-005-automated-milestone-analysis.md"
    "docs/scripts/validate-structure.mjs"
)

for file in "${analysis_files[@]}"; do
    if [[ -f "$file" ]]; then
        rm "$file"
        log_success "Removed $file"
    fi
done

echo ""
log_info "Step 6: Cleaning up any generated validation scripts..."
find docs/scripts -name "validate-*.mjs" -type f -delete 2>/dev/null || true
find docs/scripts -name "*-analysis-*.md" -type f -delete 2>/dev/null || true
log_success "Removed generated validation scripts"

echo ""
log_info "Step 7: Final git status check..."
git status --short

echo ""
log_success "🎉 Repository cleanup complete!"
echo ""
echo "The repository is now clean and ready for redesigning the milestone research system."
echo ""
echo "Next steps:"
echo "  1. Design the new milestone research conductor system"
echo "  2. Create docs/scripts/milestone-research/ directory"  
echo "  3. Implement agent-guided deep research functionality"
