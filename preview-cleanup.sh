#!/usr/bin/env bash
set -euo pipefail

# Preview what the cleanup script will ask about
echo "🔍 Preview: Files that cleanup script will ask about"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo -e "${BLUE}Files that will be individually confirmed for deletion:${NC}"

echo ""
echo -e "${YELLOW}1. Untracked analysis files:${NC}"
if [[ -f "docs/tech-specs/guides/automated-analysis-guide.mdx" ]]; then
    echo "   ✓ docs/tech-specs/guides/automated-analysis-guide.mdx"
else
    echo "   - (not found)"
fi

echo ""
echo -e "${YELLOW}2. Milestone analysis directory:${NC}"
if [[ -d "docs/scripts/milestone-analysis" ]]; then
    echo "   ✓ docs/scripts/milestone-analysis/ (entire directory)"
    echo "     Contains:"
    find docs/scripts/milestone-analysis -type f | sed 's/^/       /'
else
    echo "   - (not found)"
fi

echo ""
echo -e "${YELLOW}3. Analysis-related files:${NC}"
analysis_files=(
    "docs/tech-specs/adrs/adr-005-automated-milestone-analysis.md"
    "docs/scripts/validate-structure.mjs"
)

for file in "${analysis_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "   ✓ $file"
    else
        echo "   - $file (not found)"
    fi
done

echo ""
echo -e "${YELLOW}4. Generated validation scripts:${NC}"
generated_files=($(find docs/scripts -name "validate-*.mjs" -type f 2>/dev/null || true))
generated_files+=($(find docs/scripts -name "*-analysis-*.md" -type f 2>/dev/null || true))

if [[ ${#generated_files[@]} -gt 0 ]]; then
    for file in "${generated_files[@]}"; do
        echo "   ✓ $file"
    done
else
    echo "   - (no generated files found)"
fi

echo ""
echo -e "${GREEN}Summary:${NC}"
echo "• The script will ask 'Continue?' once to start"
echo "• Then it will ask about EACH file/directory individually"
echo "• You can say 'n' to any individual file to skip it"
echo "• Git operations (unstaging, restoring) happen automatically"

echo ""
echo "Run './cleanup-analysis-files.sh' when ready"
